{"name": "barryvdh/laravel-debugbar", "description": "PHP Debugbar integration for Laravel", "keywords": ["laravel", "debugbar", "profiler", "debug", "webprofiler"], "license": "MIT", "authors": [{"name": "Barry vd. Heuvel", "email": "<EMAIL>"}], "require": {"php": "^8.0", "maximebf/debugbar": "~1.22.0", "illuminate/routing": "^9|^10|^11", "illuminate/session": "^9|^10|^11", "illuminate/support": "^9|^10|^11", "symfony/finder": "^6|^7"}, "require-dev": {"mockery/mockery": "^1.3.3", "orchestra/testbench-dusk": "^5|^6|^7|^8|^9", "phpunit/phpunit": "^9.6|^10.5", "squizlabs/php_codesniffer": "^3.5"}, "autoload": {"psr-4": {"Barryvdh\\Debugbar\\": "src/"}, "files": ["src/helpers.php"]}, "autoload-dev": {"psr-4": {"Barryvdh\\Debugbar\\Tests\\": "tests"}}, "minimum-stability": "dev", "prefer-stable": true, "extra": {"branch-alias": {"dev-master": "3.13-dev"}, "laravel": {"providers": ["Barryvdh\\Debugbar\\ServiceProvider"], "aliases": {"Debugbar": "Barryvdh\\Debugbar\\Facades\\Debugbar"}}}, "scripts": {"check-style": "phpcs -p --standard=PSR12 config/ src/ tests/  --ignore=src/Resources/* ", "fix-style": "phpcbf -p --standard=PSR12 config/ src/ tests/  --ignore=src/Resources*", "test": "phpunit"}}