<?php

declare(strict_types=1);

namespace Doctrine\DBAL\Driver\Exception;

use Doctrine\DBAL\Driver\AbstractException;
use Throwable;

/**
 * @internal
 *
 * @psalm-immutable
 */
final class NoIdentityValue extends AbstractException
{
    public static function new(?Throwable $previous = null): self
    {
        return new self('No identity value was generated by the last statement.', null, 0, $previous);
    }
}
