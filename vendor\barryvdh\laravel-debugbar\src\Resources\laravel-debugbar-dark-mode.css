/* Dark mode */

div.phpdebugbar,
div.phpdebugbar-openhandler {
    --color-gray-100: #F7FAFC;
    --color-gray-200: #EDF2F7;
    --color-gray-300: #E2E8F0;
    --color-gray-400: #CBD5E0;
    --color-gray-500: #A0AEC0;
    --color-gray-600: #718096;
    --color-gray-700: #4A5568;
    --color-gray-800: #252a37;
    --color-gray-900: #18181b;
    --color-red-vivid: #eb4432;
}

div.phpdebugbar,
div.phpdebugbar-widgets-dataset-history .phpdebugbar-widgets-dataset-actions,
div.phpdebugbar-openhandler {
    background: var(--color-gray-800);
}

div.phpdebugbar,
div.phpdebugbar-openhandler,
div.phpdebugbar div.phpdebugbar-header > div > *,
div.phpdebugbar ul.phpdebugbar-widgets-timeline li span.phpdebugbar-widgets-label,
div.phpdebugbar ul.phpdebugbar-widgets-timeline li span.phpdebugbar-widgets-collector,
div.phpdebugbar ul.phpdebugbar-widgets-list li.phpdebugbar-widgets-list-item,
div.phpdebugbar ul.phpdebugbar-widgets-list li span.phpdebugbar-widgets-label,
div.phpdebugbar code.phpdebugbar-widgets-sql span.hljs-keyword,
div.phpdebugbar-openhandler .phpdebugbar-openhandler-header,
div.phpdebugbar-openhandler .phpdebugbar-openhandler-header a,
div.phpdebugbar-panel .phpdebugbar-widgets-dataset-actions select {
    color: var(--color-gray-200);
}

div.phpdebugbar ul.phpdebugbar-widgets-timeline li span.phpdebugbar-widgets-label {
    text-shadow: 1px 1px #000;
}

div.phpdebugbar-openhandler,
div.phpdebugbar div.phpdebugbar-widgets-messages div.phpdebugbar-widgets-toolbar,
div.phpdebugbar div.phpdebugbar-widgets-exceptions li.phpdebugbar-widgets-list-item pre.phpdebugbar-widgets-file,
div.phpdebugbar ul.phpdebugbar-widgets-list li.phpdebugbar-widgets-list-item table.phpdebugbar-widgets-params,
div.phpdebugbar-openhandler .phpdebugbar-openhandler-actions > a,
div.phpdebugbar-openhandler .phpdebugbar-openhandler-actions button,
div.phpdebugbar-openhandler .phpdebugbar-openhandler-actions > form input,
div.phpdebugbar-openhandler .phpdebugbar-openhandler-actions > form select,
div.phpdebugbar-panel .phpdebugbar-widgets-dataset-actions select {
    border-color: var(--color-gray-600);
}

div.phpdebugbar div.phpdebugbar-header,
div.phpdebugbar div.phpdebugbar-panel div.phpdebugbar-widgets-status > span:first-child:before,
div.phpdebugbar-openhandler table th,
div.phpdebugbar-openhandler .phpdebugbar-openhandler-actions > a,
div.phpdebugbar-openhandler .phpdebugbar-openhandler-actions button,
div.phpdebugbar-openhandler .phpdebugbar-openhandler-actions > form input,
div.phpdebugbar-openhandler .phpdebugbar-openhandler-actions > form select {
    text-shadow: 1px 1px var(--color-gray-700);
}

div.phpdebugbar div.phpdebugbar-header > div > select,
div.phpdebugbar ul.phpdebugbar-widgets-list li.phpdebugbar-widgets-list-item table.phpdebugbar-widgets-params,
div.phpdebugbar-openhandler .phpdebugbar-openhandler-actions > form input,
div.phpdebugbar-openhandler .phpdebugbar-openhandler-actions > form select,
div.phpdebugbar-panel .phpdebugbar-widgets-dataset-actions select,
div.phpdebugbar input[type='text'],
div.phpdebugbar input[type='password'] {
    background-color: var(--color-gray-800);
}

div.phpdebugbar div.phpdebugbar-header,
div.phpdebugbar a.phpdebugbar-restore-btn,
div.phpdebugbar-openhandler .phpdebugbar-openhandler-header,
div.phpdebugbar dl.phpdebugbar-widgets-kvlist > :nth-child(4n-1),
div.phpdebugbar dl.phpdebugbar-widgets-kvlist > :nth-child(4n),
div.phpdebugbar ul.phpdebugbar-widgets-list li.phpdebugbar-widgets-list-item:nth-child(even),
div.phpdebugbar .hljs,
div.phpdebugbar div.phpdebugbar-widgets-sqlqueries table.phpdebugbar-widgets-params th,
div.phpdebugbar-openhandler .phpdebugbar-openhandler-actions > a,
div.phpdebugbar-openhandler .phpdebugbar-openhandler-actions button,
div.phpdebugbar div.phpdebugbar-widgets-templates table.phpdebugbar-widgets-params th {
    background-color: var(--color-gray-900);
}

div.phpdebugbar .phpdebugbar-widgets-mails ul.phpdebugbar-widgets-list li.phpdebugbar-widgets-list-item .phpdebugbar-widgets-headers,
div.phpdebugbar ul.phpdebugbar-widgets-list li.phpdebugbar-widgets-list-item table.phpdebugbar-widgets-params {
    border-left-color: var(--color-gray-600);
}

div.phpdebugbar a.phpdebugbar-tab:hover,
div.phpdebugbar span.phpdebugbar-indicator:hover,
div.phpdebugbar a.phpdebugbar-indicator:hover,
div.phpdebugbar a.phpdebugbar-close-btn:hover,
div.phpdebugbar a.phpdebugbar-minimize-btn:hover,
div.phpdebugbar a.phpdebugbar-maximize-btn:hover,
div.phpdebugbar a.phpdebugbar-open-btn:hover,
div.phpdebugbar-openhandler table th,
div.phpdebugbar-openhandler table tr:nth-child(2n),
div.phpdebugbar div.phpdebugbar-widgets-messages div.phpdebugbar-widgets-toolbar {
    background-color: var(--color-gray-700);
}

div.phpdebugbar .phpdebugbar-indicator span.phpdebugbar-tooltip,
div.phpdebugbar div.phpdebugbar-mini-design a.phpdebugbar-tab:hover span.phpdebugbar-text,
div.phpdebugbar pre.sf-dump,
div.phpdebugbar .hljs,
div.phpdebugbar code.phpdebugbar-widgets-sql span.hljs-operator {
    color: var(--color-gray-100);
}

div.phpdebugbar pre.sf-dump .sf-dump-public,
div.phpdebugbar pre.sf-dump .sf-dump-protected,
div.phpdebugbar pre.sf-dump .sf-dump-private {
    color: var(--color-gray-100) !important;
}

div.phpdebugbar div.phpdebugbar-panel div.phpdebugbar-widgets-status > span:first-child:before,
div.phpdebugbar-openhandler a {
    color: var(--color-gray-500);
}

div.phpdebugbar .phpdebugbar-indicator span.phpdebugbar-tooltip,
div.phpdebugbar div.phpdebugbar-mini-design a.phpdebugbar-tab:hover span.phpdebugbar-text {
    background: var(--color-gray-900);
}

div.phpdebugbar .hljs-tag .hljs-value,
div.phpdebugbar .hljs-phpdoc,
div.phpdebugbar .tex .hljs-formula,
div.phpdebugbar div.phpdebugbar-widgets-exceptions li.phpdebugbar-widgets-list-item span.phpdebugbar-widgets-message {
    color: var(--color-red-vivid);
}

div.phpdebugbar div.phpdebugbar-widgets-sqlqueries li.phpdebugbar-widgets-list-item span.phpdebugbar-widgets-database,
div.phpdebugbar div.phpdebugbar-widgets-sqlqueries li.phpdebugbar-widgets-list-item span.phpdebugbar-widgets-duration,
div.phpdebugbar div.phpdebugbar-widgets-sqlqueries li.phpdebugbar-widgets-list-item span.phpdebugbar-widgets-memory,
div.phpdebugbar div.phpdebugbar-widgets-sqlqueries li.phpdebugbar-widgets-list-item span.phpdebugbar-widgets-row-count,
div.phpdebugbar div.phpdebugbar-widgets-sqlqueries li.phpdebugbar-widgets-list-item span.phpdebugbar-widgets-copy-clipboard,
div.phpdebugbar div.phpdebugbar-widgets-sqlqueries li.phpdebugbar-widgets-list-item span.phpdebugbar-widgets-stmt-id,
div.phpdebugbar .phpdebugbar-widgets-callgraph pre,
div.phpdebugbar .phpdebugbar-text-muted,
div.phpdebugbar-openhandler .phpdebugbar-text-muted
{
    color: var(--color-gray-600);
}

li.phpdebugbar-widgets-list-item span.phpdebugbar-widgets-filename,
li.phpdebugbar-widgets-list-item span.phpdebugbar-widgets-filename a.phpdebugbar-widgets-editor-link
{
    color: #ddd;
}
div.phpdebugbar div.phpdebugbar-widgets-sqlqueries li.phpdebugbar-widgets-list-item.phpdebugbar-widgets-sql-duplicate {
    background-color: #473e00;
}

div.phpdebugbar-widgets-messages li.phpdebugbar-widgets-list-item span.phpdebugbar-widgets-value:before {
    color: #7B7B7B;
}

div.phpdebugbar pre.sf-dump .sf-dump-note {
    color: #8be9fd;
}

div.phpdebugbar pre.sf-dump .sf-dump-num,
div.phpdebugbar pre.sf-dump .sf-dump-const,
div.phpdebugbar pre.sf-dump .sf-dump-index {
    color: #bd93f9;
}

div.phpdebugbar pre.sf-dump .sf-dump-key,
div.phpdebugbar pre.sf-dump .sf-dump-str {
    color: #f1fa8c;
}

div.phpdebugbar-openhandler {
    border-top-color: var(--color-red-vivid);
}

div.phpdebugbar div.phpdebugbar-header .phpdebugbar-tab {
    border-left-color: var(--color-gray-800);
}

div.phpdebugbar div.phpdebugbar-body {
    border-top-color: var(--color-gray-800);
}

div.phpdebugbar a.phpdebugbar-restore-btn {
    border-right-color: var(--color-gray-800) !important;
}

div.phpdebugbar span.phpdebugbar-indicator,
div.phpdebugbar a.phpdebugbar-indicator,
div.phpdebugbar a.phpdebugbar-close-btn {
    border-right-color: var(--color-gray-800);
}

div.phpdebugbar div.phpdebugbar-panel div.phpdebugbar-widgets-status {
    background-color: var(--color-gray-900) !important;
    border-bottom-color: var(--color-gray-800) !important;
}

div.phpdebugbar div.phpdebugbar-widgets-templates div.phpdebugbar-widgets-status {
    background: var(--color-gray-900) !important;
}

div.phpdebugbar div.phpdebugbar-panel div.phpdebugbar-widgets-status > * {
    color: var(--color-gray-200) !important;
}

div.phpdebugbar div.phpdebugbar-widgets-templates span.phpdebugbar-widgets-render-time,
div.phpdebugbar div.phpdebugbar-widgets-templates span.phpdebugbar-widgets-memory,
div.phpdebugbar div.phpdebugbar-widgets-templates span.phpdebugbar-widgets-param-count,
div.phpdebugbar div.phpdebugbar-widgets-templates span.phpdebugbar-widgets-type {
    color: var(--color-gray-600) !important;
}

div.phpdebugbar div.phpdebugbar-widgets-sqlqueries table.phpdebugbar-widgets-params td {
    border-color: var(--color-gray-600) !important;
}

div.phpdebugbar code,
div.phpdebugbar pre {
    color: #f1fa8c;
}

div.phpdebugbar-openhandler .phpdebugbar-openhandler-actions > a,
div.phpdebugbar-openhandler .phpdebugbar-openhandler-actions button,
div.phpdebugbar-openhandler .phpdebugbar-openhandler-actions > form input,
div.phpdebugbar-openhandler .phpdebugbar-openhandler-actions > form select,
div.phpdebugbar input[type='text'],
div.phpdebugbar input[type='password'] {
    color: var(--color-gray-300);
}

div.phpdebugbar div.phpdebugbar-widgets-exceptions li.phpdebugbar-widgets-list-item.phpdebugbar-widgets-sql-duplicate span.phpdebugbar-widgets-filename,
div.phpdebugbar div.phpdebugbar-widgets-sqlqueries li.phpdebugbar-widgets-list-item.phpdebugbar-widgets-sql-duplicate span.phpdebugbar-widgets-database,
div.phpdebugbar div.phpdebugbar-widgets-sqlqueries li.phpdebugbar-widgets-list-item.phpdebugbar-widgets-sql-duplicate span.phpdebugbar-widgets-duration,
div.phpdebugbar div.phpdebugbar-widgets-sqlqueries li.phpdebugbar-widgets-list-item.phpdebugbar-widgets-sql-duplicate span.phpdebugbar-widgets-memory,
div.phpdebugbar div.phpdebugbar-widgets-sqlqueries li.phpdebugbar-widgets-list-item.phpdebugbar-widgets-sql-duplicate span.phpdebugbar-widgets-row-count,
div.phpdebugbar div.phpdebugbar-widgets-sqlqueries li.phpdebugbar-widgets-list-item.phpdebugbar-widgets-sql-duplicate span.phpdebugbar-widgets-copy-clipboard,
div.phpdebugbar div.phpdebugbar-widgets-sqlqueries li.phpdebugbar-widgets-list-item.phpdebugbar-widgets-sql-duplicate span.phpdebugbar-widgets-stmt-id {
    color: var(--color-gray-500);
}

div.phpdebugbar dl.phpdebugbar-widgets-kvlist > :nth-child(4n)::before {
    background-color: var(--color-gray-900);
}

dt.phpdebugbar-widgets-key {
    background: var(--color-gray-800);
}

a.phpdebugbar-minimize-btn {
    background:url(data:image/svg+xml,%3Csvg%20viewBox=%220%200%201792%201792%22%20fill=%22none%22%20xmlns=%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d=%22m1683%20653.5-742%20741c-12.667%2012.67-27.667%2019-45%2019s-32.333-6.33-45-19l-742-741c-12.667-12.667-19-27.833-19-45.5s6.333-32.833%2019-45.5l166-165c12.667-12.667%2027.667-19%2045-19s32.333%206.333%2045%2019l531%20531%20531-531c12.67-12.667%2027.67-19%2045-19s32.33%206.333%2045%2019l166%20165c12.67%2012.667%2019%2027.833%2019%2045.5s-6.33%2032.833-19%2045.5Z%22%20fill=%22%23EDF2F7%22%2F%3E%3C%2Fsvg%3E) no-repeat center / 14px 14px;
}

a.phpdebugbar-maximize-btn {
    background: url(data:image/svg+xml,%3Csvg%20viewBox=%220%200%201792%201792%22%20fill=%22none%22%20xmlns=%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d=%22m1683%201229.5-166%20165c-12.67%2012.67-27.67%2019-45%2019s-32.33-6.33-45-19l-531-531-531%20531c-12.667%2012.67-27.667%2019-45%2019s-32.333-6.33-45-19l-166-165c-12.667-12.67-19-27.83-19-45.5s6.333-32.83%2019-45.5l742-741c12.667-12.667%2027.667-19%2045-19s32.333%206.333%2045%2019l742%20741c12.67%2012.67%2019%2027.83%2019%2045.5s-6.33%2032.83-19%2045.5Z%22%20fill=%22%23EDF2F7%22%2F%3E%3C%2Fsvg%3E) no-repeat center / 14px 14px;
}

a.phpdebugbar-close-btn {
    background: url(data:image/svg+xml,%3Csvg%20viewBox=%220%200%201792%201792%22%20fill=%22none%22%20xmlns=%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d=%22M1490%201258c0%2026.67-9.33%2049.33-28%2068l-136%20136c-18.67%2018.67-41.33%2028-68%2028s-49.33-9.33-68-28l-294-294-294%20294c-18.667%2018.67-41.333%2028-68%2028s-49.333-9.33-68-28l-136-136c-18.667-18.67-28-41.33-28-68s9.333-49.33%2028-68l294-294-294-294c-18.667-18.667-28-41.333-28-68s9.333-49.333%2028-68l136-136c18.667-18.667%2041.333-28%2068-28s49.333%209.333%2068%2028l294%20294%20294-294c18.67-18.667%2041.33-28%2068-28s49.33%209.333%2068%2028l136%20136c18.67%2018.667%2028%2041.333%2028%2068s-9.33%2049.333-28%2068l-294%20294%20294%20294c18.67%2018.67%2028%2041.33%2028%2068Z%22%20fill=%22%23EDF2F7%22%2F%3E%3C%2Fsvg%3E) no-repeat center / 14px 14px;
}

a.phpdebugbar-open-btn {
    background: url(data:image/svg+xml,%3Csvg%20width%3D%221792%22%20height%3D%221792%22%20fill%3D%22none%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cpath%20d%3D%22M1646%20991.797c0%2016.493-8.25%2034.063-24.75%2052.683l-268.22%20316.13c-22.89%2027.14-54.95%2050.16-96.2%2069.05S1177.4%201458%201142.27%201458H273.728c-18.095%200-34.194-3.46-48.297-10.38-14.104-6.92-21.155-18.36-21.155-34.32%200-16.5%208.249-34.06%2024.747-52.69l268.228-316.13c22.884-27.14%2054.949-50.155%2096.194-69.048%2041.246-18.893%2079.431-28.34%20114.556-28.34h868.549c18.09%200%2034.19%203.458%2048.3%2010.377%2014.1%206.918%2021.15%2018.362%2021.15%2034.328Zm-273.82-274.615V844.91H708.001c-50.027%200-102.448%2012.639-157.264%2037.918-54.817%2025.28-98.457%2057.078-130.921%2095.397L150.79%201294.35l-3.992%204.79c0-2.13-.133-5.46-.399-9.98-.266-4.52-.399-7.85-.399-9.98V512.818c0-48.962%2017.563-91.005%2052.688-126.13C233.813%20351.562%20275.856%20334%20324.819%20334h255.455c48.962%200%2091.005%2017.562%20126.13%2052.688%2035.126%2035.125%2052.688%2077.168%2052.688%20126.13v25.545h434.278c48.96%200%2091%2017.564%20126.13%2052.689%2035.12%2035.125%2052.68%2077.168%2052.68%20126.13Z%22%20fill%3D%22%23EDF2F7%22/%3E%3C/svg%3E) no-repeat center / 14px 14px;
}

div.phpdebugbar code.language-php,
div.phpdebugbar pre.language-php {
    color: #FFF;
}

pre.phpdebugbar-widgets-code-block ul {
    background: none;
}

/* Dracula Theme v1.2.5
 *
 * https://github.com/dracula/highlightjs
 *
 * Copyright 2016-present, All rights reserved
 *
 * Code licensed under the MIT license
 *
 * <AUTHOR> Ciccale <<EMAIL>>
 * <AUTHOR> Rocha <<EMAIL>>
 */

div.phpdebugbar .hljs-built_in,
div.phpdebugbar .hljs-selector-tag,
div.phpdebugbar .hljs-section,
div.phpdebugbar .hljs-link {
    color: #8be9fd;
}

div.phpdebugbar .hljs-keyword {
    color: #ff79c6;
}

div.phpdebugbar .hljs,
div.phpdebugbar .hljs-subst {
    color: #f8f8f2;
}

div.phpdebugbar .hljs-title,
div.phpdebugbar .hljs-meta-keyword {
    color: #50fa7b;
}

div.phpdebugbar .hljs-string,
div.phpdebugbar .hljs-meta,
div.phpdebugbar .hljs-name,
div.phpdebugbar .hljs-type,
div.phpdebugbar .hljs-attr,
div.phpdebugbar .hljs-symbol,
div.phpdebugbar .hljs-bullet,
div.phpdebugbar .hljs-addition,
div.phpdebugbar .hljs-variable,
div.phpdebugbar .hljs-template-tag,
div.phpdebugbar .hljs-template-variable {
    color: #f1fa8c;
}

div.phpdebugbar .hljs-comment,
div.phpdebugbar .hljs-quote,
div.phpdebugbar .hljs-deletion {
    color: #6272a4;
}

div.phpdebugbar .hljs-strong {
    font-weight: bold;
}

div.phpdebugbar .hljs-literal,
div.phpdebugbar .hljs-number {
    color: #bd93f9;
}

.phpdebugbar-widgets-dataset-history table tr.phpdebugbar-widgets-active {
    background-color: var(--color-gray-700);
    color: white;
}

.phpdebugbar-widgets-dataset-history table th,
.phpdebugbar-widgets-dataset-history table td {
    border-color: var(--color-gray-600);
}

ul.phpdebugbar-widgets-timeline table.phpdebugbar-widgets-params td {
    border-color: var(--color-gray-700);
}

