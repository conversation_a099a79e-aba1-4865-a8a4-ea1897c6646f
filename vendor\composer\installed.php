<?php return array(
    'root' => array(
        'name' => 'laravel/laravel',
        'pretty_version' => 'dev-main',
        'version' => 'dev-main',
        'reference' => 'baed3fc83d6aaa45137b6f09928f4ab038ce9a03',
        'type' => 'project',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        'anandsiddharth/laravel-paytm-wallet' => array(
            'pretty_version' => 'v2.0.0',
            'version' => '2.0.0.0',
            'reference' => '5342add0719e9c5ca94fdd13f766e6a810f2ef45',
            'type' => 'library',
            'install_path' => __DIR__ . '/../anandsiddharth/laravel-paytm-wallet',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'anhskohbo/no-captcha' => array(
            'pretty_version' => '3.6.0',
            'version' => '3.6.0.0',
            'reference' => '6f129419a7f0d0a1ed9849fdaaed34e6d83a03cc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../anhskohbo/no-captcha',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'arkitecht/laravel-twilio' => array(
            'pretty_version' => '1.7.0',
            'version' => '1.7.0.0',
            'reference' => 'f1a4aa212a329900dbd99305fc8be32c75a7bbea',
            'type' => 'library',
            'install_path' => __DIR__ . '/../arkitecht/laravel-twilio',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'aws/aws-crt-php' => array(
            'pretty_version' => 'v1.2.6',
            'version' => '1.2.6.0',
            'reference' => 'a63485b65b6b3367039306496d49737cf1995408',
            'type' => 'library',
            'install_path' => __DIR__ . '/../aws/aws-crt-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'aws/aws-sdk-php' => array(
            'pretty_version' => '3.321.8',
            'version' => '3.321.8.0',
            'reference' => 'df456658bdc2ad84a00cf35a7b5af874fdcc7a53',
            'type' => 'library',
            'install_path' => __DIR__ . '/../aws/aws-sdk-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'barryvdh/laravel-debugbar' => array(
            'pretty_version' => 'v3.13.5',
            'version' => '3.13.5.0',
            'reference' => '92d86be45ee54edff735e46856f64f14b6a8bb07',
            'type' => 'library',
            'install_path' => __DIR__ . '/../barryvdh/laravel-debugbar',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'brick/math' => array(
            'pretty_version' => '0.12.1',
            'version' => '0.12.1.0',
            'reference' => 'f510c0a40911935b77b86859eb5223d58d660df1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../brick/math',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'carbonphp/carbon-doctrine-types' => array(
            'pretty_version' => '3.2.0',
            'version' => '3.2.0.0',
            'reference' => '18ba5ddfec8976260ead6e866180bd5d2f71aa1d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../carbonphp/carbon-doctrine-types',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'composer/ca-bundle' => array(
            'pretty_version' => '1.5.1',
            'version' => '1.5.1.0',
            'reference' => '063d9aa8696582f5a41dffbbaf3c81024f0a604a',
            'type' => 'library',
            'install_path' => __DIR__ . '/./ca-bundle',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'composer/semver' => array(
            'pretty_version' => '3.4.2',
            'version' => '3.4.2.0',
            'reference' => 'c51258e759afdb17f1fd1fe83bc12baaef6309d6',
            'type' => 'library',
            'install_path' => __DIR__ . '/./semver',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'cordoval/hamcrest-php' => array(
            'dev_requirement' => true,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'davedevelopment/hamcrest-php' => array(
            'dev_requirement' => true,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'dflydev/dot-access-data' => array(
            'pretty_version' => 'v3.0.3',
            'version' => '3.0.3.0',
            'reference' => 'a23a2bf4f31d3518f3ecb38660c95715dfead60f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dflydev/dot-access-data',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/annotations' => array(
            'pretty_version' => '1.14.4',
            'version' => '1.14.4.0',
            'reference' => '253dca476f70808a5aeed3a47cc2cc88c5cab915',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/annotations',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/common' => array(
            'pretty_version' => '3.4.4',
            'version' => '3.4.4.0',
            'reference' => '0aad4b7ab7ce8c6602dfbb1e1a24581275fb9d1a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/common',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/dbal' => array(
            'pretty_version' => '4.1.1',
            'version' => '4.1.1.0',
            'reference' => '7a8252418689feb860ea8dfeab66d64a56a64df8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/dbal',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/deprecations' => array(
            'pretty_version' => '1.1.3',
            'version' => '1.1.3.0',
            'reference' => 'dfbaa3c2d2e9a9df1118213f3b8b0c597bb99fab',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/deprecations',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/event-manager' => array(
            'pretty_version' => '2.0.1',
            'version' => '2.0.1.0',
            'reference' => 'b680156fa328f1dfd874fd48c7026c41570b9c6e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/event-manager',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/inflector' => array(
            'pretty_version' => '2.0.10',
            'version' => '2.0.10.0',
            'reference' => '5817d0659c5b50c9b950feb9af7b9668e2c436bc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/inflector',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/lexer' => array(
            'pretty_version' => '2.1.1',
            'version' => '2.1.1.0',
            'reference' => '861c870e8b75f7c8f69c146c7f89cc1c0f1b49b6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/lexer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/persistence' => array(
            'pretty_version' => '3.3.3',
            'version' => '3.3.3.0',
            'reference' => 'b337726451f5d530df338fc7f68dee8781b49779',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/persistence',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'dragonmantank/cron-expression' => array(
            'pretty_version' => 'v3.3.3',
            'version' => '3.3.3.0',
            'reference' => 'adfb1f505deb6384dc8b39804c5065dd3c8c8c0a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dragonmantank/cron-expression',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'egulias/email-validator' => array(
            'pretty_version' => '4.0.2',
            'version' => '4.0.2.0',
            'reference' => 'ebaaf5be6c0286928352e054f2d5125608e5405e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../egulias/email-validator',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ezyang/htmlpurifier' => array(
            'pretty_version' => 'v4.17.0',
            'version' => '4.17.0.0',
            'reference' => 'bbc513d79acf6691fa9cf10f192c90dd2957f18c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ezyang/htmlpurifier',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'fakerphp/faker' => array(
            'pretty_version' => 'v1.23.1',
            'version' => '1.23.1.0',
            'reference' => 'bfb4fe148adbf78eff521199619b93a52ae3554b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../fakerphp/faker',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'fedapay/fedapay-php' => array(
            'pretty_version' => '0.4.2',
            'version' => '0.4.2.0',
            'reference' => '71a495e9ebd6635fd80fdea4cdbbe8615635cf84',
            'type' => 'library',
            'install_path' => __DIR__ . '/../fedapay/fedapay-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'filp/whoops' => array(
            'pretty_version' => '2.15.4',
            'version' => '2.15.4.0',
            'reference' => 'a139776fa3f5985a50b509f2a02ff0f709d2a546',
            'type' => 'library',
            'install_path' => __DIR__ . '/../filp/whoops',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'firebase/php-jwt' => array(
            'pretty_version' => 'v6.10.1',
            'version' => '6.10.1.0',
            'reference' => '500501c2ce893c824c801da135d02661199f60c5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../firebase/php-jwt',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'fruitcake/php-cors' => array(
            'pretty_version' => 'v1.3.0',
            'version' => '1.3.0.0',
            'reference' => '3d158f36e7875e2f040f37bc0573956240a5a38b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../fruitcake/php-cors',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'google/apiclient' => array(
            'pretty_version' => 'v2.17.0',
            'version' => '2.17.0.0',
            'reference' => 'b1f63d72c44307ec8ef7bf18f1012de35d8944ed',
            'type' => 'library',
            'install_path' => __DIR__ . '/../google/apiclient',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'google/apiclient-services' => array(
            'pretty_version' => 'v0.372.0',
            'version' => '0.372.0.0',
            'reference' => '61b9426f6351ac3c652f6d9ba014c89fc446f764',
            'type' => 'library',
            'install_path' => __DIR__ . '/../google/apiclient-services',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'google/auth' => array(
            'pretty_version' => 'v1.42.0',
            'version' => '1.42.0.0',
            'reference' => '0c25599a91530b5847f129b271c536f75a7563f5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../google/auth',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'graham-campbell/result-type' => array(
            'pretty_version' => 'v1.1.3',
            'version' => '1.1.3.0',
            'reference' => '3ba905c11371512af9d9bdd27d99b782216b6945',
            'type' => 'library',
            'install_path' => __DIR__ . '/../graham-campbell/result-type',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/guzzle' => array(
            'pretty_version' => '7.9.2',
            'version' => '7.9.2.0',
            'reference' => 'd281ed313b989f213357e3be1a179f02196ac99b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/guzzle',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/promises' => array(
            'pretty_version' => '2.0.3',
            'version' => '2.0.3.0',
            'reference' => '6ea8dd08867a2a42619d65c3deb2c0fcbf81c8f8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/promises',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/psr7' => array(
            'pretty_version' => '2.7.0',
            'version' => '2.7.0.0',
            'reference' => 'a70f5c95fb43bc83f07c9c948baa0dc1829bf201',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/psr7',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/uri-template' => array(
            'pretty_version' => 'v1.0.3',
            'version' => '1.0.3.0',
            'reference' => 'ecea8feef63bd4fef1f037ecb288386999ecc11c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/uri-template',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hamcrest/hamcrest-php' => array(
            'pretty_version' => 'v2.0.1',
            'version' => '2.0.1.0',
            'reference' => '8c3d0a3f6af734494ad8f6fbbee0ba92422859f3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hamcrest/hamcrest-php',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'illuminate/auth' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.22.0',
            ),
        ),
        'illuminate/broadcasting' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.22.0',
            ),
        ),
        'illuminate/bus' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.22.0',
            ),
        ),
        'illuminate/cache' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.22.0',
            ),
        ),
        'illuminate/collections' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.22.0',
            ),
        ),
        'illuminate/conditionable' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.22.0',
            ),
        ),
        'illuminate/config' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.22.0',
            ),
        ),
        'illuminate/console' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.22.0',
            ),
        ),
        'illuminate/container' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.22.0',
            ),
        ),
        'illuminate/contracts' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.22.0',
            ),
        ),
        'illuminate/cookie' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.22.0',
            ),
        ),
        'illuminate/database' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.22.0',
            ),
        ),
        'illuminate/encryption' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.22.0',
            ),
        ),
        'illuminate/events' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.22.0',
            ),
        ),
        'illuminate/filesystem' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.22.0',
            ),
        ),
        'illuminate/hashing' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.22.0',
            ),
        ),
        'illuminate/http' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.22.0',
            ),
        ),
        'illuminate/log' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.22.0',
            ),
        ),
        'illuminate/macroable' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.22.0',
            ),
        ),
        'illuminate/mail' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.22.0',
            ),
        ),
        'illuminate/notifications' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.22.0',
            ),
        ),
        'illuminate/pagination' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.22.0',
            ),
        ),
        'illuminate/pipeline' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.22.0',
            ),
        ),
        'illuminate/process' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.22.0',
            ),
        ),
        'illuminate/queue' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.22.0',
            ),
        ),
        'illuminate/redis' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.22.0',
            ),
        ),
        'illuminate/routing' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.22.0',
            ),
        ),
        'illuminate/session' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.22.0',
            ),
        ),
        'illuminate/support' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.22.0',
            ),
        ),
        'illuminate/testing' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.22.0',
            ),
        ),
        'illuminate/translation' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.22.0',
            ),
        ),
        'illuminate/validation' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.22.0',
            ),
        ),
        'illuminate/view' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.22.0',
            ),
        ),
        'iyzico/iyzipay-php' => array(
            'pretty_version' => 'v2.0.55',
            'version' => '********',
            'reference' => '4f6abcd1f4a1ec5db3f6d840e08cd57c910aeff2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../iyzico/iyzipay-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'kkomelin/laravel-translatable-string-exporter' => array(
            'pretty_version' => '1.22.0',
            'version' => '********',
            'reference' => '0c6dbec4694a7e702830ecfc005d131cd5ffe402',
            'type' => 'library',
            'install_path' => __DIR__ . '/../kkomelin/laravel-translatable-string-exporter',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'kodova/hamcrest-php' => array(
            'dev_requirement' => true,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'konekt/html' => array(
            'pretty_version' => '6.5.0',
            'version' => '*******',
            'reference' => '599dfd0986a7a125170ed8897904cd65452a4e54',
            'type' => 'library',
            'install_path' => __DIR__ . '/../konekt/html',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'lab404/laravel-impersonate' => array(
            'pretty_version' => '1.7.5',
            'version' => '1.7.5.0',
            'reference' => '82cad73700a8699d63de169bb41abd5ae283e9a8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../lab404/laravel-impersonate',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'lahirulhr/laravel-payhere' => array(
            'pretty_version' => 'v1.0.4',
            'version' => '1.0.4.0',
            'reference' => '72ce0aa87b495517e3b2d66633e588c86d417e61',
            'type' => 'library',
            'install_path' => __DIR__ . '/../lahirulhr/laravel-payhere',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/framework' => array(
            'pretty_version' => 'v11.22.0',
            'version' => '1********',
            'reference' => '868c75beacc47d0f361b919bbc155c0b619bf3d5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/framework',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/laravel' => array(
            'pretty_version' => 'dev-main',
            'version' => 'dev-main',
            'reference' => 'baed3fc83d6aaa45137b6f09928f4ab038ce9a03',
            'type' => 'project',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/pint' => array(
            'pretty_version' => 'v1.17.3',
            'version' => '1.17.3.0',
            'reference' => '9d77be916e145864f10788bb94531d03e1f7b482',
            'type' => 'project',
            'install_path' => __DIR__ . '/../laravel/pint',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'laravel/prompts' => array(
            'pretty_version' => 'v0.1.25',
            'version' => '0.1.25.0',
            'reference' => '7b4029a84c37cb2725fc7f011586e2997040bc95',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/prompts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/sail' => array(
            'pretty_version' => 'v1.31.3',
            'version' => '1.31.3.0',
            'reference' => '0a7e2891a85eba2d448a9ffc6fc5ce367e924bc1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/sail',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'laravel/sanctum' => array(
            'pretty_version' => 'v4.0.3',
            'version' => '4.0.3.0',
            'reference' => '54aea9d13743ae8a6cdd3c28dbef128a17adecab',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/sanctum',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/serializable-closure' => array(
            'pretty_version' => 'v1.3.4',
            'version' => '1.3.4.0',
            'reference' => '61b87392d986dc49ad5ef64e75b1ff5fee24ef81',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/serializable-closure',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/tinker' => array(
            'pretty_version' => 'v2.9.0',
            'version' => '2.9.0.0',
            'reference' => '502e0fe3f0415d06d5db1f83a472f0f3b754bafe',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/tinker',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/ui' => array(
            'pretty_version' => 'v4.5.2',
            'version' => '4.5.2.0',
            'reference' => 'c75396f63268c95b053c8e4814eb70e0875e9628',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/ui',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravelcollective/html' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '^6.4',
            ),
        ),
        'league/commonmark' => array(
            'pretty_version' => '2.5.3',
            'version' => '2.5.3.0',
            'reference' => 'b650144166dfa7703e62a22e493b853b58d874b0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/commonmark',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/config' => array(
            'pretty_version' => 'v1.2.0',
            'version' => '1.2.0.0',
            'reference' => '754b3604fb2984c71f4af4a9cbe7b57f346ec1f3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/config',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/flysystem' => array(
            'pretty_version' => '3.28.0',
            'version' => '3.28.0.0',
            'reference' => 'e611adab2b1ae2e3072fa72d62c62f52c2bf1f0c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/flysystem',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/flysystem-aws-s3-v3' => array(
            'pretty_version' => '3.28.0',
            'version' => '3.28.0.0',
            'reference' => '22071ef1604bc776f5ff2468ac27a752514665c8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/flysystem-aws-s3-v3',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/flysystem-local' => array(
            'pretty_version' => '3.28.0',
            'version' => '3.28.0.0',
            'reference' => '13f22ea8be526ea58c2ddff9e158ef7c296e4f40',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/flysystem-local',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/mime-type-detection' => array(
            'pretty_version' => '1.15.0',
            'version' => '1.15.0.0',
            'reference' => 'ce0f4d1e8a6f4eb0ddff33f57c69c50fd09f4301',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/mime-type-detection',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'maatwebsite/excel' => array(
            'pretty_version' => '3.1.58',
            'version' => '********',
            'reference' => '18495a71b112f43af8ffab35111a58b4e4ba4a4d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../maatwebsite/excel',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'maennchen/zipstream-php' => array(
            'pretty_version' => '3.1.0',
            'version' => '*******',
            'reference' => 'b8174494eda667f7d13876b4a7bfef0f62a7c0d1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../maennchen/zipstream-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'markbaker/complex' => array(
            'pretty_version' => '3.0.2',
            'version' => '*******',
            'reference' => '95c56caa1cf5c766ad6d65b6344b807c1e8405b9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../markbaker/complex',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'markbaker/matrix' => array(
            'pretty_version' => '3.0.1',
            'version' => '*******',
            'reference' => '728434227fe21be27ff6d86621a1b13107a2562c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../markbaker/matrix',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mashape/unirest-php' => array(
            'pretty_version' => 'v3.0.4',
            'version' => '*******',
            'reference' => '842c0f242dfaaf85f16b72e217bf7f7c19ab12cb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mashape/unirest-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'maximebf/debugbar' => array(
            'pretty_version' => 'v1.22.5',
            'version' => '1.22.5.0',
            'reference' => '1b5cabe0ce013134cf595bfa427bbf2f6abcd989',
            'type' => 'library',
            'install_path' => __DIR__ . '/../maximebf/debugbar',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'mercadopago/dx-php' => array(
            'pretty_version' => '2.6.2',
            'version' => '2.6.2.0',
            'reference' => 'f5f97bd96dfcb3bafdfba634b3bc757025238caa',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mercadopago/dx-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'midtrans/midtrans-php' => array(
            'pretty_version' => '2.5.2',
            'version' => '2.5.2.0',
            'reference' => 'a1ad0c824449ca8c68c4cf11b3417ad518311d2b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../midtrans/midtrans-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'milon/barcode' => array(
            'pretty_version' => 'v11.0.0',
            'version' => '11.0.0.0',
            'reference' => '1f4031adb52146bb7fbe88ef42214011376f9cbe',
            'type' => 'library',
            'install_path' => __DIR__ . '/../milon/barcode',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mockery/mockery' => array(
            'pretty_version' => '1.6.12',
            'version' => '1.6.12.0',
            'reference' => '1f4efdd7d3beafe9807b08156dfcb176d18f1699',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mockery/mockery',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'mollie/mollie-api-php' => array(
            'pretty_version' => 'v2.71.0',
            'version' => '2.71.0.0',
            'reference' => 'dff324f0621ff134fbefffa42ee511833a58578f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mollie/mollie-api-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'monolog/monolog' => array(
            'pretty_version' => '3.7.0',
            'version' => '3.7.0.0',
            'reference' => 'f4393b648b78a5408747de94fca38beb5f7e9ef8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../monolog/monolog',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mpdf/mpdf' => array(
            'pretty_version' => 'v8.2.5',
            'version' => '8.2.5.0',
            'reference' => 'e175b05e3e00977b85feb96a8cccb174ac63621f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mpdf/mpdf',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mpdf/psr-http-message-shim' => array(
            'pretty_version' => 'v2.0.1',
            'version' => '2.0.1.0',
            'reference' => 'f25a0153d645e234f9db42e5433b16d9b113920f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mpdf/psr-http-message-shim',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mpdf/psr-log-aware-trait' => array(
            'pretty_version' => 'v3.0.0',
            'version' => '3.0.0.0',
            'reference' => 'a633da6065e946cc491e1c962850344bb0bf3e78',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mpdf/psr-log-aware-trait',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mtdowling/cron-expression' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '^1.0',
            ),
        ),
        'mtdowling/jmespath.php' => array(
            'pretty_version' => '2.8.0',
            'version' => '2.8.0.0',
            'reference' => 'a2a865e05d5f420b50cc2f85bb78d565db12a6bc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mtdowling/jmespath.php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'munafio/chatify' => array(
            'pretty_version' => 'v1.6.3',
            'version' => '1.6.3.0',
            'reference' => '559ff515fc83a822ed72cdd03ca8e36c574c5a25',
            'type' => 'library',
            'install_path' => __DIR__ . '/../munafio/chatify',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'myclabs/deep-copy' => array(
            'pretty_version' => '1.12.0',
            'version' => '1.12.0.0',
            'reference' => '3a6b9a42cd8f8771bd4295d13e1423fa7f3d942c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../myclabs/deep-copy',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nesbot/carbon' => array(
            'pretty_version' => '3.8.0',
            'version' => '3.8.0.0',
            'reference' => 'bbd3eef89af8ba66a3aa7952b5439168fbcc529f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nesbot/carbon',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nette/schema' => array(
            'pretty_version' => 'v1.3.0',
            'version' => '1.3.0.0',
            'reference' => 'a6d3a6d1f545f01ef38e60f375d1cf1f4de98188',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nette/schema',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nette/utils' => array(
            'pretty_version' => 'v4.0.5',
            'version' => '4.0.5.0',
            'reference' => '736c567e257dbe0fcf6ce81b4d6dbe05c6899f96',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nette/utils',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nikic/php-parser' => array(
            'pretty_version' => 'v5.1.0',
            'version' => '5.1.0.0',
            'reference' => '683130c2ff8c2739f4822ff7ac5c873ec529abd1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nikic/php-parser',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nunomaduro/collision' => array(
            'pretty_version' => 'v8.4.0',
            'version' => '8.4.0.0',
            'reference' => 'e7d1aa8ed753f63fa816932bbc89678238843b4a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nunomaduro/collision',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'nunomaduro/termwind' => array(
            'pretty_version' => 'v2.1.0',
            'version' => '2.1.0.0',
            'reference' => 'e5f21eade88689536c0cdad4c3cd75f3ed26e01a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nunomaduro/termwind',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nwidart/laravel-modules' => array(
            'pretty_version' => 'v11.1.4',
            'version' => '11.1.4.0',
            'reference' => 'fb1f6bd7b168baaa6212dee678c18fc983d47ed4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nwidart/laravel-modules',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'obydul/laraskrill' => array(
            'pretty_version' => 'v1.2.0',
            'version' => '1.2.0.0',
            'reference' => 'b3f936b3acd9fd1e44857e946d6fde2090ea64e4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../obydul/laraskrill',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'orhanerday/open-ai' => array(
            'pretty_version' => '5.2',
            'version' => '5.2.0.0',
            'reference' => 'd8c78fe2f5fed59e0ba458f90b5589ed9f13a367',
            'type' => 'library',
            'install_path' => __DIR__ . '/../orhanerday/open-ai',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'paragonie/constant_time_encoding' => array(
            'pretty_version' => 'v3.0.0',
            'version' => '3.0.0.0',
            'reference' => 'df1e7fde177501eee2037dd159cf04f5f301a512',
            'type' => 'library',
            'install_path' => __DIR__ . '/../paragonie/constant_time_encoding',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'paragonie/random_compat' => array(
            'pretty_version' => 'v9.99.100',
            'version' => '9.99.100.0',
            'reference' => '996434e5492cb4c3edcb9168db6fbb1359ef965a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../paragonie/random_compat',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'paragonie/sodium_compat' => array(
            'pretty_version' => 'v1.21.1',
            'version' => '1.21.1.0',
            'reference' => 'bb312875dcdd20680419564fe42ba1d9564b9e37',
            'type' => 'library',
            'install_path' => __DIR__ . '/../paragonie/sodium_compat',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'paymentwall/paymentwall-php' => array(
            'pretty_version' => '2.2.4',
            'version' => '2.2.4.0',
            'reference' => '68064bfa12d00e856998b205c6b364ff8d7bc906',
            'type' => 'library',
            'install_path' => __DIR__ . '/../paymentwall/paymentwall-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phar-io/manifest' => array(
            'pretty_version' => '2.0.4',
            'version' => '2.0.4.0',
            'reference' => '54750ef60c58e43759730615a392c31c80e23176',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phar-io/manifest',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phar-io/version' => array(
            'pretty_version' => '3.2.1',
            'version' => '3.2.1.0',
            'reference' => '4f7fd7836c6f332bb2933569e566a0d6c4cbed74',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phar-io/version',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'php-ds/php-ds' => array(
            'pretty_version' => 'v1.5.0',
            'version' => '1.5.0.0',
            'reference' => '7b2c5f1843466d50769a0682ce6fa9ddaaa99cb4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-ds/php-ds',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpoffice/phpspreadsheet' => array(
            'pretty_version' => '1.29.1',
            'version' => '1.29.1.0',
            'reference' => '59ee38f7480904cd6487e5cbdea4d80ff2758719',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpoffice/phpspreadsheet',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpoption/phpoption' => array(
            'pretty_version' => '1.9.3',
            'version' => '1.9.3.0',
            'reference' => 'e3fac8b24f56113f7cb96af14958c0dd16330f54',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpoption/phpoption',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpseclib/phpseclib' => array(
            'pretty_version' => '3.0.41',
            'version' => '3.0.41.0',
            'reference' => '621c73f7dcb310b61de34d1da4c4204e8ace6ceb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpseclib/phpseclib',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpunit/php-code-coverage' => array(
            'pretty_version' => '11.0.6',
            'version' => '11.0.6.0',
            'reference' => 'ebdffc9e09585dafa71b9bffcdb0a229d4704c45',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-code-coverage',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-file-iterator' => array(
            'pretty_version' => '5.1.0',
            'version' => '5.1.0.0',
            'reference' => '118cfaaa8bc5aef3287bf315b6060b1174754af6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-file-iterator',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-invoker' => array(
            'pretty_version' => '5.0.1',
            'version' => '5.0.1.0',
            'reference' => 'c1ca3814734c07492b3d4c5f794f4b0995333da2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-invoker',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-text-template' => array(
            'pretty_version' => '4.0.1',
            'version' => '4.0.1.0',
            'reference' => '3e0404dc6b300e6bf56415467ebcb3fe4f33e964',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-text-template',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-timer' => array(
            'pretty_version' => '7.0.1',
            'version' => '7.0.1.0',
            'reference' => '3b415def83fbcb41f991d9ebf16ae4ad8b7837b3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-timer',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/phpunit' => array(
            'pretty_version' => '11.3.4',
            'version' => '11.3.4.0',
            'reference' => 'd2ef57db1410b102b250e0cdce6675a60c2a993d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/phpunit',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'psr/cache' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => 'aa5030cfa5405eccfdcb1083ce040c2cb8d253bf',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/clock' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => 'e41a24703d4560fd0acb709162f73b8adfc3aa0d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/clock',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/clock-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/container' => array(
            'pretty_version' => '2.0.2',
            'version' => '2.0.2.0',
            'reference' => 'c71ecc56dfe541dbd90c5360474fbc405f8d5963',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/container',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/container-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.1|2.0',
            ),
        ),
        'psr/event-dispatcher' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => 'dbefd12671e8a14ec7f180cab83036ed26714bb0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/event-dispatcher',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/event-dispatcher-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-client' => array(
            'pretty_version' => '1.0.3',
            'version' => '1.0.3.0',
            'reference' => 'bb5906edc1c324c9a05aa0873d40117941e5fa90',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-client',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-factory' => array(
            'pretty_version' => '1.1.0',
            'version' => '1.1.0.0',
            'reference' => '2b4765fddfe3b508ac62f829e852b1501d3f6e8a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-factory',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-factory-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-message' => array(
            'pretty_version' => '2.0',
            'version' => '2.0.0.0',
            'reference' => '402d35bcb92c70c026d1a6a9883f06b2ead23d71',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-message-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/log' => array(
            'pretty_version' => '3.0.1',
            'version' => '*******',
            'reference' => '79dff0b268932c640297f5208d6298f71855c03e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/log-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0|2.0|3.0',
                1 => '3.0.0',
            ),
        ),
        'psr/simple-cache' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => '764e0b3939f5ca87cb904f570ef9be2d78a07865',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/simple-cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/simple-cache-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0|2.0|3.0',
            ),
        ),
        'psy/psysh' => array(
            'pretty_version' => 'v0.12.4',
            'version' => '0.12.4.0',
            'reference' => '2fd717afa05341b4f8152547f142cd2f130f6818',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psy/psysh',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'pusher/pusher-php-server' => array(
            'pretty_version' => '7.2.4',
            'version' => '7.2.4.0',
            'reference' => 'de2f72296808f9cafa6a4462b15a768ff130cddb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../pusher/pusher-php-server',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'rachidlaasri/laravel-installer' => array(
            'pretty_version' => '4.1.0',
            'version' => '4.1.0.0',
            'reference' => 'b751b4c23dba893e9a4a12f881a6fd8fa921d228',
            'type' => 'library',
            'install_path' => __DIR__ . '/../rachidlaasri/laravel-installer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ralouphie/getallheaders' => array(
            'pretty_version' => '3.0.3',
            'version' => '3.0.3.0',
            'reference' => '120b605dfeb996808c31b6477290a714d356e822',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ralouphie/getallheaders',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ramsey/collection' => array(
            'pretty_version' => '2.0.0',
            'version' => '2.0.0.0',
            'reference' => 'a4b48764bfbb8f3a6a4d1aeb1a35bb5e9ecac4a5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ramsey/collection',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ramsey/uuid' => array(
            'pretty_version' => '4.7.6',
            'version' => '4.7.6.0',
            'reference' => '91039bc1faa45ba123c4328958e620d382ec7088',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ramsey/uuid',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'rhumsaa/uuid' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '4.7.6',
            ),
        ),
        'sebastian/cli-parser' => array(
            'pretty_version' => '3.0.2',
            'version' => '*******',
            'reference' => '15c5dd40dc4f38794d383bb95465193f5e0ae180',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/cli-parser',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/code-unit' => array(
            'pretty_version' => '3.0.1',
            'version' => '*******',
            'reference' => '6bb7d09d6623567178cf54126afa9c2310114268',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/code-unit',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/code-unit-reverse-lookup' => array(
            'pretty_version' => '4.0.1',
            'version' => '4.0.1.0',
            'reference' => '183a9b2632194febd219bb9246eee421dad8d45e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/code-unit-reverse-lookup',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/comparator' => array(
            'pretty_version' => '6.0.2',
            'version' => '6.0.2.0',
            'reference' => '450d8f237bd611c45b5acf0733ce43e6bb280f81',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/comparator',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/complexity' => array(
            'pretty_version' => '4.0.1',
            'version' => '4.0.1.0',
            'reference' => 'ee41d384ab1906c68852636b6de493846e13e5a0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/complexity',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/diff' => array(
            'pretty_version' => '6.0.2',
            'version' => '6.0.2.0',
            'reference' => 'b4ccd857127db5d41a5b676f24b51371d76d8544',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/diff',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/environment' => array(
            'pretty_version' => '7.2.0',
            'version' => '7.2.0.0',
            'reference' => '855f3ae0ab316bbafe1ba4e16e9f3c078d24a0c5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/environment',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/exporter' => array(
            'pretty_version' => '6.1.3',
            'version' => '6.1.3.0',
            'reference' => 'c414673eee9a8f9d51bbf8d61fc9e3ef1e85b20e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/exporter',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/global-state' => array(
            'pretty_version' => '7.0.2',
            'version' => '7.0.2.0',
            'reference' => '3be331570a721f9a4b5917f4209773de17f747d7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/global-state',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/lines-of-code' => array(
            'pretty_version' => '3.0.1',
            'version' => '*******',
            'reference' => 'd36ad0d782e5756913e42ad87cb2890f4ffe467a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/lines-of-code',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/object-enumerator' => array(
            'pretty_version' => '6.0.1',
            'version' => '6.0.1.0',
            'reference' => 'f5b498e631a74204185071eb41f33f38d64608aa',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/object-enumerator',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/object-reflector' => array(
            'pretty_version' => '4.0.1',
            'version' => '4.0.1.0',
            'reference' => '6e1a43b411b2ad34146dee7524cb13a068bb35f9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/object-reflector',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/recursion-context' => array(
            'pretty_version' => '6.0.2',
            'version' => '6.0.2.0',
            'reference' => '694d156164372abbd149a4b85ccda2e4670c0e16',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/recursion-context',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/type' => array(
            'pretty_version' => '5.0.1',
            'version' => '5.0.1.0',
            'reference' => 'fb6a6566f9589e86661291d13eba708cce5eb4aa',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/type',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/version' => array(
            'pretty_version' => '5.0.1',
            'version' => '5.0.1.0',
            'reference' => '45c9debb7d039ce9b97de2f749c2cf5832a06ac4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/version',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'setasign/fpdi' => array(
            'pretty_version' => 'v2.6.3',
            'version' => '2.6.3.0',
            'reference' => '67c31f5e50c93c20579ca9e23035d8c540b51941',
            'type' => 'library',
            'install_path' => __DIR__ . '/../setasign/fpdi',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'spatie/backtrace' => array(
            'pretty_version' => '1.6.2',
            'version' => '1.6.2.0',
            'reference' => '1a9a145b044677ae3424693f7b06479fc8c137a9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/backtrace',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'spatie/browsershot' => array(
            'pretty_version' => '4.3.0',
            'version' => '4.3.0.0',
            'reference' => '601f2758191d8c46b2ea587eea935a87da4f39e8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/browsershot',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'spatie/error-solutions' => array(
            'pretty_version' => '1.1.1',
            'version' => '1.1.1.0',
            'reference' => 'ae7393122eda72eed7cc4f176d1e96ea444f2d67',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/error-solutions',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'spatie/flare-client-php' => array(
            'pretty_version' => '1.8.0',
            'version' => '1.8.0.0',
            'reference' => '180f8ca4c0d0d6fc51477bd8c53ce37ab5a96122',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/flare-client-php',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'spatie/ignition' => array(
            'pretty_version' => '1.15.0',
            'version' => '1.15.0.0',
            'reference' => 'e3a68e137371e1eb9edc7f78ffa733f3b98991d2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/ignition',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'spatie/laravel-google-calendar' => array(
            'pretty_version' => '3.8.0',
            'version' => '3.8.0.0',
            'reference' => '1532b897524ed876a24aee00616a72db74ef05a7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/laravel-google-calendar',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'spatie/laravel-ignition' => array(
            'pretty_version' => '2.8.0',
            'version' => '2.8.0.0',
            'reference' => '3c067b75bfb50574db8f7e2c3978c65eed71126c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/laravel-ignition',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'spatie/laravel-package-tools' => array(
            'pretty_version' => '1.16.5',
            'version' => '1.16.5.0',
            'reference' => 'c7413972cf22ffdff97b68499c22baa04eddb6a2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/laravel-package-tools',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'spatie/laravel-permission' => array(
            'pretty_version' => '6.9.0',
            'version' => '6.9.0.0',
            'reference' => 'fe973a58b44380d0e8620107259b7bda22f70408',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/laravel-permission',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'spatie/once' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'spatie/temporary-directory' => array(
            'pretty_version' => '2.2.1',
            'version' => '2.2.1.0',
            'reference' => '76949fa18f8e1a7f663fd2eaa1d00e0bcea0752a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/temporary-directory',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'srmklive/paypal' => array(
            'pretty_version' => '3.0.32',
            'version' => '********',
            'reference' => '031d69d7c99f9ef0874a34cb85326ede28cd7aed',
            'type' => 'library',
            'install_path' => __DIR__ . '/../srmklive/paypal',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'stripe/stripe-php' => array(
            'pretty_version' => 'v15.8.0',
            'version' => '********',
            'reference' => '5ed133fa45987771f80ad300be2316c05832f6a7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../stripe/stripe-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/clock' => array(
            'pretty_version' => 'v7.1.1',
            'version' => '*******',
            'reference' => '3dfc8b084853586de51dd1441c6242c76a28cbe7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/clock',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/console' => array(
            'pretty_version' => 'v7.1.4',
            'version' => '*******',
            'reference' => '1eed7af6961d763e7832e874d7f9b21c3ea9c111',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/console',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/css-selector' => array(
            'pretty_version' => 'v7.1.1',
            'version' => '*******',
            'reference' => '1c7cee86c6f812896af54434f8ce29c8d94f9ff4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/css-selector',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/deprecation-contracts' => array(
            'pretty_version' => 'v3.5.0',
            'version' => '3.5.0.0',
            'reference' => '0e0d29ce1f20deffb4ab1b016a7257c4f1e789a1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/deprecation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/error-handler' => array(
            'pretty_version' => 'v7.1.3',
            'version' => '7.1.3.0',
            'reference' => '432bb369952795c61ca1def65e078c4a80dad13c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/error-handler',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher' => array(
            'pretty_version' => 'v7.1.1',
            'version' => '*******',
            'reference' => '9fa7f7a21beb22a39a8f3f28618b29e50d7a55a7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/event-dispatcher',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher-contracts' => array(
            'pretty_version' => 'v3.5.0',
            'version' => '3.5.0.0',
            'reference' => '8f93aec25d41b72493c6ddff14e916177c9efc50',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/event-dispatcher-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '2.0|3.0',
            ),
        ),
        'symfony/finder' => array(
            'pretty_version' => 'v7.1.4',
            'version' => '*******',
            'reference' => 'd95bbf319f7d052082fb7af147e0f835a695e823',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/finder',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/http-foundation' => array(
            'pretty_version' => 'v7.1.3',
            'version' => '7.1.3.0',
            'reference' => 'f602d5c17d1fa02f8019ace2687d9d136b7f4a1a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-foundation',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/http-kernel' => array(
            'pretty_version' => 'v7.1.4',
            'version' => '*******',
            'reference' => '6efcbd1b3f444f631c386504fc83eeca25963747',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-kernel',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/mailer' => array(
            'pretty_version' => 'v7.1.2',
            'version' => '7.1.2.0',
            'reference' => '8fcff0af9043c8f8a8e229437cea363e282f9aee',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/mailer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/mime' => array(
            'pretty_version' => 'v7.1.4',
            'version' => '*******',
            'reference' => 'ccaa6c2503db867f472a587291e764d6a1e58758',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/mime',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-ctype' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => 'a3cc8b044a6ea513310cbd48ef7333b384945638',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-ctype',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-grapheme' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => 'b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-grapheme',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-idn' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => 'c36586dcf89a12315939e00ec9b4474adcb1d773',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-idn',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-normalizer' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => '3833d7255cc303546435cb650316bff708a1c75c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-normalizer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => '85181ba99b2345b0ef10ce42ecac37612d9fd341',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php80' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => '60328e362d4c2c802a54fcbf04f9d3fb892b4cf8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php80',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php83' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => '2fb86d65e2d424369ad2905e83b236a8805ba491',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php83',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-uuid' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => '21533be36c24be3f4b1669c4725c7d1d2bab4ae2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-uuid',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/process' => array(
            'pretty_version' => 'v7.1.3',
            'version' => '7.1.3.0',
            'reference' => '7f2f542c668ad6c313dc4a5e9c3321f733197eca',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/process',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/routing' => array(
            'pretty_version' => 'v7.1.4',
            'version' => '*******',
            'reference' => '1500aee0094a3ce1c92626ed8cf3c2037e86f5a7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/routing',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/service-contracts' => array(
            'pretty_version' => 'v3.5.0',
            'version' => '3.5.0.0',
            'reference' => 'bd1d9e59a81d8fa4acdcea3f617c581f7475a80f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/service-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/string' => array(
            'pretty_version' => 'v7.1.4',
            'version' => '*******',
            'reference' => '6cd670a6d968eaeb1c77c2e76091c45c56bc367b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/string',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation' => array(
            'pretty_version' => 'v7.1.3',
            'version' => '7.1.3.0',
            'reference' => '8d5e50c813ba2859a6dfc99a0765c550507934a1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/translation',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation-contracts' => array(
            'pretty_version' => 'v3.5.0',
            'version' => '3.5.0.0',
            'reference' => 'b9d2189887bb6b2e0367a9fc7136c5239ab9b05a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/translation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '2.3|3.0',
            ),
        ),
        'symfony/uid' => array(
            'pretty_version' => 'v7.1.4',
            'version' => '*******',
            'reference' => '82177535395109075cdb45a70533aa3d7a521cdf',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/uid',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/var-dumper' => array(
            'pretty_version' => 'v7.1.4',
            'version' => '*******',
            'reference' => 'a5fa7481b199090964d6fd5dab6294d5a870c7aa',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/var-dumper',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/yaml' => array(
            'pretty_version' => 'v7.1.4',
            'version' => '*******',
            'reference' => '92e080b851c1c655c786a2da77f188f2dccd0f4b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/yaml',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'theseer/tokenizer' => array(
            'pretty_version' => '1.2.3',
            'version' => '1.2.3.0',
            'reference' => '737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../theseer/tokenizer',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'tijsverkoyen/css-to-inline-styles' => array(
            'pretty_version' => 'v2.2.7',
            'version' => '2.2.7.0',
            'reference' => '83ee6f38df0a63106a9e4536e3060458b74ccedb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../tijsverkoyen/css-to-inline-styles',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'twilio/sdk' => array(
            'pretty_version' => '7.16.2',
            'version' => '7.16.2.0',
            'reference' => '02ad214b0cc9fc513bd67df251d54aed8901284f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../twilio/sdk',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'vlucas/phpdotenv' => array(
            'pretty_version' => 'v5.6.1',
            'version' => '5.6.1.0',
            'reference' => 'a59a13791077fe3d44f90e7133eb68e7d22eaff2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../vlucas/phpdotenv',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'voku/portable-ascii' => array(
            'pretty_version' => '2.0.1',
            'version' => '2.0.1.0',
            'reference' => 'b56450eed252f6801410d810c8e1727224ae0743',
            'type' => 'library',
            'install_path' => __DIR__ . '/../voku/portable-ascii',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'webmozart/assert' => array(
            'pretty_version' => '1.11.0',
            'version' => '1.11.0.0',
            'reference' => '11cb2199493b2f8a3b53e7f19068fc6aac760991',
            'type' => 'library',
            'install_path' => __DIR__ . '/../webmozart/assert',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'whichbrowser/parser' => array(
            'pretty_version' => 'v2.1.8',
            'version' => '2.1.8.0',
            'reference' => '581d614d686bfbec3529ad60562a5213ac5d8d72',
            'type' => 'library',
            'install_path' => __DIR__ . '/../whichbrowser/parser',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'wikimedia/composer-merge-plugin' => array(
            'pretty_version' => 'v2.1.0',
            'version' => '2.1.0.0',
            'reference' => 'a03d426c8e9fb2c9c569d9deeb31a083292788bc',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/../wikimedia/composer-merge-plugin',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'yoomoney/yookassa-sdk-php' => array(
            'pretty_version' => '3.5.0',
            'version' => '3.5.0.0',
            'reference' => 'a1c423afc6f2e906cfec38f8c84c96419bf78728',
            'type' => 'library',
            'install_path' => __DIR__ . '/../yoomoney/yookassa-sdk-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'yoomoney/yookassa-sdk-validator' => array(
            'pretty_version' => '1.0.1',
            'version' => '1.0.1.0',
            'reference' => '0726f254113cd48881ac6b90bf2d9645e6c5a285',
            'type' => 'library',
            'install_path' => __DIR__ . '/../yoomoney/yookassa-sdk-validator',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
